"use client";

import React, { useState, useEffect, useRef, useCallback } from "react";
import { <PERSON><PERSON>, Send, Sparkles, BookOpen, X, LogIn, ChevronDown, MoreHorizontal } from "lucide-react";
import { useDispatch, useSelector } from "react-redux";
import { actions } from "@/features/aiscript/aiscript.slice";
import { actions as socialActions } from "@/features/social-listening/social-listening.slice";
import {
  selectAiscriptMessages,
  selectAiscriptLoading,
  selectAiscriptOpen,
  selectPromptManager,
  selectIsMaximized,
  selectSavePromptModal,
  selectUserPrompts,
} from "@/features/aiscript/aiscript.slice";
import { selectIsAuthenticated } from "@/features/auth/auth.slice";
import { QuickReply } from "@/features/aiscript/aiscript.slice";
import { selectTranscriptText } from "@/features/social-listening/social-listening.slice";
import ReactMarkdown from "react-markdown";
import PromptManager from "@/components/AiChat/PromptManagerNew";
import SavePromptModal from "@/components/AiChat/SavePromptModal";

export default function AiScript() {
  const dispatch = useDispatch();
  const messages = useSelector(selectAiscriptMessages);
  const isLoading = useSelector(selectAiscriptLoading);
  const isOpen = useSelector(selectAiscriptOpen);
  const promptManager = useSelector(selectPromptManager);
  const isMaximized = useSelector(selectIsMaximized);
  const savePromptModal = useSelector(selectSavePromptModal);
  const userPrompts = useSelector(selectUserPrompts);
  const [inputMessage, setInputMessage] = useState("");
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const isAuth = useSelector(selectIsAuthenticated);

  const [isExpanded, setIsExpanded] = useState(false);
  const [isClosing, setIsClosing] = useState(false);
  const [copiedMessageIndex, setCopiedMessageIndex] = useState<number | null>(
    null
  );

  // Quick replies dropdown state
  const [quickRepliesDropdownOpen, setQuickRepliesDropdownOpen] = useState(false);
  const [quickRepliesMessageIndex, setQuickRepliesMessageIndex] = useState<number | null>(null);
  const [selectedQuickReplyIndex, setSelectedQuickReplyIndex] = useState(0);
  const [dropdownPositionAbove, setDropdownPositionAbove] = useState(false);
  const quickRepliesDropdownRef = useRef<HTMLDivElement>(null);
  const quickRepliesTriggerRef = useRef<HTMLButtonElement>(null);
  const transcriptText = useSelector(selectTranscriptText);

  // Add tracking for transcript mode and store the transcript text
  const [isTranscriptMode, setIsTranscriptMode] = useState(false);
  const currentTranscriptRef = useRef<string | null>(null);

  // Track when we should show quick replies for transcript
  const [shouldShowTranscriptOptions, setShouldShowTranscriptOptions] =
    useState(false);



  // Slash commands state
  const [showSlashCommands, setShowSlashCommands] = useState(false);
  const [slashQuery, setSlashQuery] = useState('');
  const [selectedSlashIndex, setSelectedSlashIndex] = useState(0);
  const slashDropdownRef = useRef<HTMLDivElement>(null);

  // Filter prompts for slash commands
  const filteredSlashPrompts = React.useMemo(() => {
    if (!slashQuery) return userPrompts.slice(0, 4); // Show first 4 prompts

    const query = slashQuery.toLowerCase();
    return userPrompts
      .filter(prompt =>
        prompt.title.toLowerCase().includes(query) ||
        prompt.content.toLowerCase().includes(query)
      )
      .slice(0, 4); // Limit to 4 results
  }, [userPrompts, slashQuery]);

  // Scroll selected item into view
  const scrollSelectedIntoView = useCallback(() => {
    if (slashDropdownRef.current && selectedSlashIndex >= 0) {
      const selectedElement = slashDropdownRef.current.children[selectedSlashIndex] as HTMLElement;
      if (selectedElement) {
        selectedElement.scrollIntoView({
          behavior: 'smooth',
          block: 'nearest',
        });
      }
    }
  }, [selectedSlashIndex]);

  // Scroll into view when selected index changes
  useEffect(() => {
    if (showSlashCommands) {
      scrollSelectedIntoView();
    }
  }, [selectedSlashIndex, showSlashCommands, scrollSelectedIntoView]);

  // Reset selected index when filtered prompts change
  useEffect(() => {
    if (selectedSlashIndex >= filteredSlashPrompts.length) {
      setSelectedSlashIndex(0);
    }
  }, [filteredSlashPrompts.length, selectedSlashIndex]);

  // Add state for typing animation
  const [typingIndex, setTypingIndex] = useState<number | null>(null);
  const [typingText, setTypingText] = useState("");
  const [typingComplete, setTypingComplete] = useState(true);
  const typingSpeed = 15; // ms per character
  const maxTypingDuration = 3000; // Maximum typing animation duration (3s)

  // Add ref for textarea
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Scroll to bottom when messages update or during typing
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages, isOpen, typingText]);

  // Close message menus and slash commands when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;

      // Close message menus
      if (!target.closest('.message-menu') && !target.closest('[data-menu-trigger]')) {
        const menus = document.querySelectorAll('.message-menu');
        menus.forEach(menu => menu.classList.add('hidden'));
      }

      // Close slash commands dropdown
      if (!target.closest('textarea') && !target.closest('.slash-commands-dropdown')) {
        setShowSlashCommands(false);
        setSlashQuery('');
        setSelectedSlashIndex(0);
      }
    };

    document.addEventListener('click', handleClickOutside);
    return () => document.removeEventListener('click', handleClickOutside);
  }, []);

  // Monitor for new messages to animate
  useEffect(() => {
    if (!isLoading && messages.length > 0 && typingComplete) {
      const firstMessage = messages[0];

      console.log("firstMessage", firstMessage);

      // Check if this is a transcript message that needs quick replies
      if (
        firstMessage.type === "ai" &&
        firstMessage.content?.includes("Original script:")
      ) {
        setShouldShowTranscriptOptions(true);
        setIsTranscriptMode(true);

        // Store the transcript for future use
        const scriptContent = firstMessage.content
          .split("Original script:")[1]
          ?.trim();
        if (scriptContent) {
          currentTranscriptRef.current = scriptContent;
        }
      }

      // Only animate AI messages
      if (firstMessage.type === "ai") {
        startTypingAnimation(firstMessage.content, messages.length - 1);
      }
    }
  }, [messages, isLoading]);

  // Reset transcript mode when conversation is cleared or session ends
  useEffect(() => {
    if (messages.length === 0) {
      setIsTranscriptMode(false);
      currentTranscriptRef.current = null;
    }
  }, [messages.length]);

  // Listen for multiple conditions to show transcript quick replies
  useEffect(() => {
    if (shouldShowTranscriptOptions && typingComplete && transcriptText) {
      // Extract just the transcript part
      let extractedTranscript = transcriptText;
      // Remove "Original script:" if present
      if (transcriptText.includes("Original script:")) {
        extractedTranscript = extractedTranscript
          .replace("Original script:", "")
          .trim();
      }

      // Mark as processed to prevent showing quick replies multiple times
      setShouldShowTranscriptOptions(false);
      dispatch(socialActions.setTranscript(null));
    }
  }, [shouldShowTranscriptOptions, typingComplete, transcriptText]);

  // Function to animate text typing
  const startTypingAnimation = (text: string, index: number) => {
    // If it's already typing or no text to type, do nothing
    if (!typingComplete || !text) return;

    setTypingIndex(index);
    setTypingText("");
    setTypingComplete(false);

    let charIndex = -1;
    const startTime = Date.now();

    const typeChar = () => {
      // If we've exceeded max duration, show full text immediately
      if (Date.now() - startTime > maxTypingDuration) {
        setTypingText(text);
        setTypingComplete(true);
        setTypingIndex(null);
        return;
      }

      // If we've typed all characters
      if (charIndex >= text.length) {
        setTypingComplete(true);
        setTypingIndex(null);
        return;
      }

      // Add the next character
      setTypingText((prev) => prev + text.charAt(charIndex));
      charIndex++;

      // Schedule the next character
      setTimeout(typeChar, typingSpeed);
    };

    // Start typing
    typeChar();
  };

  // Add function to auto-resize textarea
  const adjustTextareaHeight = () => {
    const textarea = textareaRef.current;
    if (textarea) {
      // Reset height first to get the correct scrollHeight
      textarea.style.height = "auto";
      // Set the height to scrollHeight with a maximum
      const maxHeight = 150; // maximum height in pixels
      textarea.style.height = `${Math.min(textarea.scrollHeight, maxHeight)}px`;
    }
  };

  // Adjust textarea height when input message changes
  useEffect(() => {
    adjustTextareaHeight();
  }, [inputMessage]);

  const handleSendMessage = () => {
    if (!inputMessage.trim() || isLoading) return;

    console.log("currentTranscriptRef.current", currentTranscriptRef.current);
    console.log("isTranscriptMode", isTranscriptMode);
    // Check if we're in transcript mode and this is the first user message
    if (isTranscriptMode && currentTranscriptRef.current) {
      // Append the transcript to the user's message
      const messageWithTranscript = `${inputMessage.trim()}\n\n${
        currentTranscriptRef.current
      }`;
      console.log(
        "AIScript inputMessage with transcript",
        messageWithTranscript
      );
      dispatch(
        actions.sendUserPrompt({
          displayText: inputMessage,
          content: messageWithTranscript,
        })
      );

      // Exit transcript mode after the first message
      setIsTranscriptMode(false);
    } else {
      // Regular message handling
      console.log("AIScript inputMessage", inputMessage);
      dispatch(actions.sendMessage(inputMessage));
    }

    setInputMessage("");
  };

  // Handle key press in textarea
  const handleKeyPress = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    // Handle slash command navigation
    if (showSlashCommands && filteredSlashPrompts.length > 0) {
      if (e.key === 'ArrowDown') {
        e.preventDefault();
        setSelectedSlashIndex(prev =>
          prev < filteredSlashPrompts.length - 1 ? prev + 1 : 0
        );
        return;
      }

      if (e.key === 'ArrowUp') {
        e.preventDefault();
        setSelectedSlashIndex(prev =>
          prev > 0 ? prev - 1 : filteredSlashPrompts.length - 1
        );
        return;
      }

      if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault();
        const selectedPrompt = filteredSlashPrompts[selectedSlashIndex];
        if (selectedPrompt) {
          // Use the selected prompt
          dispatch(actions.sendUserPrompt({
            displayText: selectedPrompt.title,
            content: selectedPrompt.content,
          }));
          setInputMessage('');
          setShowSlashCommands(false);
          setSlashQuery('');
          setSelectedSlashIndex(0);
        }
        return;
      }

      if (e.key === 'Escape') {
        e.preventDefault();
        setShowSlashCommands(false);
        setSlashQuery('');
        setSelectedSlashIndex(0);
        return;
      }
    }

    // If Enter is pressed without Shift, send message
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault(); // Prevent newline
      handleSendMessage();
    }
  };

  // Handle quick reply clicks
  const handleQuickReply = (reply: QuickReply) => {
    if (isLoading) return;

    // Display only the label in the UI but send the full content and promptId to the backend
    dispatch(
      actions.sendUserPrompt({
        displayText: reply.label,
        content: reply.content,
        promptId: reply.promptId, // Include the promptId if available
      })
    );

    // Exit transcript mode since we've used a quick reply
    setIsTranscriptMode(false);
  };

  // Calculate optimal dropdown position
  const calculateDropdownPosition = useCallback(() => {
    if (!quickRepliesTriggerRef.current) return false;

    const trigger = quickRepliesTriggerRef.current;
    const triggerRect = trigger.getBoundingClientRect();
    const viewportHeight = window.innerHeight;

    // Dropdown height (estimated based on max 4 items)
    const dropdownHeight = 280; // max-h-[280px]
    const padding = 20; // Safe padding from viewport edges

    // Calculate available space below and above trigger
    const spaceBelow = viewportHeight - triggerRect.bottom - padding;
    const spaceAbove = triggerRect.top - padding;

    // Position above if there's insufficient space below but sufficient space above
    return spaceBelow < dropdownHeight && spaceAbove >= dropdownHeight;
  }, []);

  // Quick replies dropdown functions
  const openQuickRepliesDropdown = useCallback((messageIndex: number) => {
    setQuickRepliesMessageIndex(messageIndex);
    setQuickRepliesDropdownOpen(true);
    setSelectedQuickReplyIndex(0);

    // Calculate and set dropdown position
    const shouldPositionAbove = calculateDropdownPosition();
    setDropdownPositionAbove(shouldPositionAbove);
  }, [calculateDropdownPosition]);

  const closeQuickRepliesDropdown = useCallback(() => {
    setQuickRepliesDropdownOpen(false);
    setQuickRepliesMessageIndex(null);
    setSelectedQuickReplyIndex(0);
    setDropdownPositionAbove(false);
  }, []);

  const toggleQuickRepliesDropdown = useCallback((messageIndex: number) => {
    if (quickRepliesDropdownOpen && quickRepliesMessageIndex === messageIndex) {
      closeQuickRepliesDropdown();
    } else {
      openQuickRepliesDropdown(messageIndex);
    }
  }, [quickRepliesDropdownOpen, quickRepliesMessageIndex, openQuickRepliesDropdown, closeQuickRepliesDropdown]);

  // Handle click outside and keyboard navigation for dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        quickRepliesDropdownOpen &&
        quickRepliesDropdownRef.current &&
        quickRepliesTriggerRef.current &&
        !quickRepliesDropdownRef.current.contains(event.target as Node) &&
        !quickRepliesTriggerRef.current.contains(event.target as Node)
      ) {
        closeQuickRepliesDropdown();
      }
    };

    const handleKeyDown = (event: KeyboardEvent) => {
      if (!quickRepliesDropdownOpen || quickRepliesMessageIndex === null) return;

      const currentMessage = messages[quickRepliesMessageIndex];
      const quickReplies = currentMessage?.quickReplies || [];

      switch (event.key) {
        case 'Escape':
          closeQuickRepliesDropdown();
          quickRepliesTriggerRef.current?.focus();
          break;
        case 'ArrowDown':
          event.preventDefault();
          setSelectedQuickReplyIndex(prev => {
            const newIndex = prev < quickReplies.length - 1 ? prev + 1 : 0;
            // Scroll the selected item into view
            setTimeout(() => {
              const dropdown = quickRepliesDropdownRef.current;
              const selectedButton = dropdown?.querySelector(`[role="menuitem"]:nth-child(${newIndex + 1})`);
              if (selectedButton && dropdown) {
                selectedButton.scrollIntoView({
                  behavior: 'smooth',
                  block: 'nearest'
                });
              }
            }, 0);
            return newIndex;
          });
          break;
        case 'ArrowUp':
          event.preventDefault();
          setSelectedQuickReplyIndex(prev => {
            const newIndex = prev > 0 ? prev - 1 : quickReplies.length - 1;
            // Scroll the selected item into view
            setTimeout(() => {
              const dropdown = quickRepliesDropdownRef.current;
              const selectedButton = dropdown?.querySelector(`[role="menuitem"]:nth-child(${newIndex + 1})`);
              if (selectedButton && dropdown) {
                selectedButton.scrollIntoView({
                  behavior: 'smooth',
                  block: 'nearest'
                });
              }
            }, 0);
            return newIndex;
          });
          break;
        case 'Enter':
        case ' ':
          event.preventDefault();
          if (quickReplies[selectedQuickReplyIndex]) {
            handleQuickReply(quickReplies[selectedQuickReplyIndex]);
            closeQuickRepliesDropdown();
          }
          break;
      }
    };

    if (quickRepliesDropdownOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      document.addEventListener('keydown', handleKeyDown);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [quickRepliesDropdownOpen, quickRepliesMessageIndex, selectedQuickReplyIndex, messages, closeQuickRepliesDropdown, handleQuickReply]);

  const handleCloseScript = () => {
    // If expanded or maximized, collapse first then close
    if (isExpanded || isMaximized) {
      setIsClosing(true);
      setIsExpanded(false);
      dispatch(actions.setMaximized(false));

      // Wait for collapse animation to finish before closing
      setTimeout(() => {
        if (isAuth) {
          dispatch(actions.endSession());
        } else {
          dispatch(actions.closeAIScript());
          dispatch(actions.clearMessages());
        }
        setIsClosing(false);
      }, 300); // Match this duration with the CSS transition duration
    } else {
      // If not expanded or maximized, close immediately
      if (isAuth) {
        dispatch(actions.endSession());
      } else {
        dispatch(actions.closeAIScript());
        dispatch(actions.clearMessages());
      }
    }
  };

  // Toggle script when button is clicked
  const toggleScript = () => {
    dispatch(actions.toggleScript());
  };

  // Handle input change with slash command detection
  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value;
    setInputMessage(value);

    // Check for slash commands
    if (value.startsWith('/')) {
      const query = value.slice(1); // Remove the '/' character
      setSlashQuery(query);
      setShowSlashCommands(true);
      setSelectedSlashIndex(0);
    } else {
      setShowSlashCommands(false);
      setSlashQuery('');
      setSelectedSlashIndex(0);
    }
  };





  // Handle keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Only handle shortcuts when chat is open and not typing in input
      if (!isOpen || (e.target as HTMLElement)?.tagName === 'TEXTAREA') return;

      // Ctrl/Cmd + P to open prompt manager
      if ((e.ctrlKey || e.metaKey) && e.key === 'p') {
        e.preventDefault();
        if (promptManager.isOpen) {
          dispatch(actions.closePromptManager());
        } else {
          dispatch(actions.openPromptManager());
        }
      }

      // Escape to close prompt manager
      if (e.key === 'Escape' && promptManager.isOpen) {
        e.preventDefault();
        dispatch(actions.closePromptManager());
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, promptManager.isOpen, dispatch]);

  // Handle prompt population from PromptManager
  const handlePromptPopulation = (content: string) => {
    setInputMessage(content);
    // Focus the textarea and position cursor at the end
    setTimeout(() => {
      const textarea = document.querySelector('textarea[placeholder*="Ask AI"]') as HTMLTextAreaElement;
      if (textarea) {
        textarea.focus();
        textarea.setSelectionRange(content.length, content.length);
      }
    }, 100);
  };



  // Format message content with full display (no truncation)
  const formatMessageContent = (content: string) => {
    // Create a formatted content element with markdown support
    const createContentElement = (text: string) => {
      if (!text) return <span></span>;

      // Handle special case for "Original script:" heading
      const hasOriginalScript = text.includes("Original script:");

      if (hasOriginalScript) {
        const parts = text.split("Original script:");
        return (
          <>
            <ReactMarkdown>{parts[0]}</ReactMarkdown>
            {parts[1] && (
              <>
                <div className="font-bold w-full">Original script:</div>
                <ReactMarkdown>{parts[1]}</ReactMarkdown>
              </>
            )}
          </>
        );
      }

      return <ReactMarkdown>{text}</ReactMarkdown>;
    };

    if (!content) {
      return createContentElement("");
    }

    // Always return the full content without truncation
    return createContentElement(content);
  };



  // Function to copy message content to clipboard
  const handleCopyMessage = (content: string, index: number) => {
    navigator.clipboard.writeText(content).then(
      () => {
        // Set the index of the copied message to show feedback
        setCopiedMessageIndex(index);

        // Reset the copied state after 2 seconds
        setTimeout(() => {
          setCopiedMessageIndex(null);
        }, 2000);
      },
      (err) => {
        console.error("Could not copy text: ", err);
      }
    );
  };

  // If chat is closed, show floating button
  if (!isOpen) {
    return (
      <button
        onClick={toggleScript}
        className="h-10 sm:h-11 md:h-12 rounded-full bg-gray-900 hover:bg-black
  dark:bg-gray-800 dark:hover:bg-gray-700 text-white flex items-center
  justify-center shadow-lg transition-all duration-200 px-3 sm:px-4 gap-1 sm:gap-2 min-w-0"
      >
        <Sparkles size={16} className="sm:w-5 sm:h-5" />
        <span className="text-white text-xs sm:text-sm font-medium hidden xs:inline whitespace-nowrap">Affitor AI</span>
        <span className="text-white text-xs font-medium xs:hidden">Affitor AI</span>
      </button>
    );
  }

  // If chat is open, render as panel content
  return (
    <div className="h-full flex flex-col font-['Inter',_'system-ui',_sans-serif]">

      {isOpen && (
        <div className="flex-1 flex flex-col overflow-hidden bg-white dark:bg-gray-900">
          {/* Header */}
          <header
            className="flex items-center justify-between p-2 sm:p-3 border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 sticky top-0 z-10"
            role="banner"
            style={{ position: 'sticky' }}
          >
            <div className="flex items-center gap-1 sm:gap-2 min-w-0 flex-1">
              <Sparkles size={16} className="sm:w-5 sm:h-5 text-blue-600 dark:text-blue-400 flex-shrink-0" aria-hidden="true" />
              <h1 className="font-semibold text-sm sm:text-base truncate">Ask Affitor AI</h1>
            </div>
            <div className="flex items-center gap-1 flex-shrink-0">
              {/* Close Button */}
              <button
                onClick={handleCloseScript}
                className="w-7 h-7 sm:w-8 sm:h-8 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg flex items-center justify-center transition-colors text-gray-500 dark:text-gray-400"
                title="Close chat"
                aria-label="Close chat"
              >
                <X size={14} className="sm:w-4 sm:h-4" />
              </button>
            </div>
          </header>

          {/* Messages */}
          <div
            className={`flex-1 overflow-y-auto p-3 sm:p-4 md:p-6 bg-white dark:bg-gray-900 ${
              messages.length === 0
                ? "flex items-center justify-center"
                : "space-y-3 sm:space-y-4"
            } ${
              promptManager.isOpen
                ? "max-h-[30vh] sm:max-h-[40vh] md:max-h-[50vh]"
                : "max-h-[calc(100vh-200px)] sm:max-h-[calc(100vh-180px)]"
            }`}
            style={{
              minHeight: promptManager.isOpen ? '200px' : '300px'
            }}
          >
            {/* Welcome message when no messages and user is authenticated */}
            {messages.length === 0 && isAuth && (
              <div className="text-center max-w-md mx-auto">
                <div className="mb-4">
                  <Sparkles size={32} className="text-blue-600 dark:text-blue-400 mx-auto mb-3" />
                  <h2 className="font-bold text-xl text-gray-900 dark:text-gray-100 mb-2">
                    How can I help you today?
                  </h2>
                  <p className="text-gray-600 dark:text-gray-400 text-sm">
                    Ask me anything about affiliate marketing, content creation, or business strategies.
                  </p>
                </div>

                {/* Quick tip about prompt manager */}
                <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3 text-left">
                  <div className="flex items-start gap-2">
                    <BookOpen size={16} className="text-blue-600 dark:text-blue-400 mt-0.5 flex-shrink-0" />
                    <div>
                      <p className="text-sm font-medium text-blue-900 dark:text-blue-100 mb-1">
                        Pro Tip
                      </p>
                      <p className="text-xs text-blue-700 dark:text-blue-300">
                        Click the "Prompts" button below to access your saved prompts and templates.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Login required message when no messages and user is not authenticated */}
            {messages.length === 0 && !isAuth && (
              <div className="flex flex-col items-center justify-center h-full min-h-[400px] px-6">
                <div className="text-center max-w-sm">
                  {/* Icon with gradient background */}
                  <div className="w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg">
                    <LogIn size={24} className="text-white" />
                  </div>

                  {/* Main heading */}
                  <h2 className="font-bold text-2xl text-gray-900 dark:text-white mb-3">
                    Welcome to Affitor AI
                  </h2>

                  {/* Subtitle */}
                  <p className="text-gray-600 dark:text-gray-300 text-base mb-6 leading-relaxed">
                    Sign in to unlock personalized AI assistance and access your saved prompts
                  </p>

                  {/* CTA Button */}
                  <button
                    onClick={() => window.location.href = '/authentication'}
                    className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-medium py-3 px-6 rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
                  >
                    Sign In to Continue
                  </button>

                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-4">
                    New to Affitor? Create your account to get started
                  </p>
                </div>
              </div>
            )}

            {messages.map((message: any, index: number) => (
              <div
                key={index}
                className={`flex flex-col ${
                  message.type === "user" ? "items-end" : "items-start"
                }`}
              >
                {message.type === "user" ? (
                  // User message - right-aligned with margin
                  <div className="flex justify-end w-full group">
                    <div className="flex items-start gap-2 max-w-[80%]">
                      {/* Message menu */}
                      <div className="relative">
                        <button
                          data-menu-trigger
                          onClick={() => {
                            // Toggle menu for this specific message
                            const currentMenus = document.querySelectorAll('.message-menu');
                            currentMenus.forEach(menu => menu.classList.add('hidden'));
                            const menu = document.getElementById(`menu-${index}`);
                            if (menu) {
                              menu.classList.toggle('hidden');
                            }
                          }}
                          className="opacity-0 group-hover:opacity-100 p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded transition-all duration-200 mt-1"
                        >
                          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                            <circle cx="12" cy="12" r="1"/>
                            <circle cx="12" cy="5" r="1"/>
                            <circle cx="12" cy="19" r="1"/>
                          </svg>
                        </button>

                        {/* Dropdown menu */}
                        <div
                          id={`menu-${index}`}
                          className="message-menu hidden absolute right-0 top-8 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg py-1 z-10 min-w-[140px]"
                        >
                          <button
                            onClick={() => {
                              dispatch(actions.openSavePromptModal(message.content));
                              // Hide menu
                              document.getElementById(`menu-${index}`)?.classList.add('hidden');
                            }}
                            className="w-full text-left px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                          >
                            Save as prompt
                          </button>
                        </div>
                      </div>

                      <div
                        className={`bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100 rounded-2xl px-4 py-3`}
                      >
                        <div
                          className="text-sm leading-relaxed font-normal break-words whitespace-pre-wrap font-sans"
                          style={{
                            fontFamily:
                              '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
                          }}
                        >
                          {formatMessageContent(message.content)}
                        </div>
                      </div>
                    </div>
                  </div>
                ) : (
                  // AI message - plain text like Claude (no background)
                  <div className="flex justify-start w-full">
                    <div className="w-full mx-1">
                      <div
                        className={`text-sm leading-relaxed font-normal text-gray-900 dark:text-gray-100 break-words whitespace-pre-wrap font-sans ${
                          index === 0 ? "font-bold text-base" : ""
                        }`}
                        style={{
                          fontFamily:
                            '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
                        }}
                      >
                        {index === typingIndex ? (
                          <>
                            {typingText.includes("Original script:") ? (
                              <>
                                {(() => {
                                  const parts =
                                    typingText.split("Original script:");
                                  return (
                                    <>
                                      <ReactMarkdown
                                        components={{
                                          p: ({ children }) => (
                                            <p className="mb-1">{children}</p>
                                          ),
                                          h1: ({ children }) => (
                                            <h1 className="text-lg font-bold mb-2">
                                              {children}
                                            </h1>
                                          ),
                                          h2: ({ children }) => (
                                            <h2 className="text-base font-bold mb-1.5">
                                              {children}
                                            </h2>
                                          ),
                                        }}
                                      >
                                        {parts[0]}
                                      </ReactMarkdown>
                                      {parts[1] && (
                                        <>
                                          <div className="font-bold w-full mb-1">
                                            Original script:
                                          </div>
                                          <ReactMarkdown
                                            components={{
                                              p: ({ children }) => (
                                                <p className="mb-1">
                                                  {children}
                                                </p>
                                              ),
                                            }}
                                          >
                                            {parts[1]}
                                          </ReactMarkdown>
                                        </>
                                      )}
                                    </>
                                  );
                                })()}
                              </>
                            ) : (
                              <ReactMarkdown
                                components={{
                                  p: ({ children }) => (
                                    <p className="mb-1">{children}</p>
                                  ),
                                  h1: ({ children }) => (
                                    <h1 className="text-lg font-bold mb-2">
                                      {children}
                                    </h1>
                                  ),
                                  h2: ({ children }) => (
                                    <h2 className="text-base font-bold mb-1.5">
                                      {children}
                                    </h2>
                                  ),
                                }}
                              >
                                {typingText}
                              </ReactMarkdown>
                            )}
                            <span className="inline-block w-2 h-4 bg-gray-400 dark:bg-gray-500 ml-1 animate-pulse"></span>
                          </>
                        ) : (
                          formatMessageContent(message.content)
                        )}
                      </div>

                      {/* Action buttons for AI messages */}
                      {message.type === "ai" &&
                        (index !== typingIndex || typingComplete) && (
                          <div className="flex items-center gap-2 mt-3">
                            {/* Copy button */}
                            <button
                              onClick={() =>
                                handleCopyMessage(message.content, index)
                              }
                              className="px-1 sm:px-0 py-1 sm:py-0 hover:bg-gray-100 dark:hover:bg-gray-800 sm:hover:p-[1px] rounded-md transition-all duration-200 flex items-center gap-1 sm:gap-1.5 text-xs text-gray-500 dark:text-gray-400 font-sans"
                              style={{
                                fontFamily:
                                  '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
                              }}
                              title="Copy response"
                            >
                              {copiedMessageIndex === index ? (
                                <>
                                  <span className="text-green-600 dark:text-green-400">
                                    ✓
                                  </span>
                                  <span className="text-green-600 dark:text-green-400 hidden xs:inline">
                                    Copied
                                  </span>
                                </>
                              ) : (
                                <>
                                  <Copy size={12} />
                                  <span className="hidden xs:inline">Copy</span>
                                </>
                              )}
                            </button>

                            {/* Quick replies trigger - only show for last AI message with quick replies */}
                            {index === messages.length - 1 &&
                              message.quickReplies &&
                              message.quickReplies.length > 0 && (
                                <div className="relative">
                                  <button
                                    ref={quickRepliesTriggerRef}
                                    onClick={() => toggleQuickRepliesDropdown(index)}
                                    onMouseEnter={() => openQuickRepliesDropdown(index)}
                                    className="px-1 sm:px-0 py-1 sm:py-0 hover:bg-gray-100 dark:hover:bg-gray-800 sm:hover:p-[1px] rounded-md transition-all duration-200 flex items-center gap-1 sm:gap-1.5 text-xs text-gray-500 dark:text-gray-400 font-sans"
                                    style={{
                                      fontFamily:
                                        '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
                                    }}
                                    title="Quick replies"
                                    aria-expanded={quickRepliesDropdownOpen && quickRepliesMessageIndex === index}
                                    aria-haspopup="menu"
                                  >
                                    <MoreHorizontal size={12} />
                                    <span className="hidden xs:inline">Quick replies</span>
                                    <span className="xs:hidden">Replies</span>
                                    <ChevronDown
                                      size={10}
                                      className={`transition-transform duration-200 ${
                                        quickRepliesDropdownOpen && quickRepliesMessageIndex === index
                                          ? 'rotate-180'
                                          : ''
                                      }`}
                                    />
                                  </button>

                                  {/* Quick replies dropdown */}
                                  {quickRepliesDropdownOpen && quickRepliesMessageIndex === index && (
                                    <div
                                      ref={quickRepliesDropdownRef}
                                      className={`absolute left-0 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-50 min-w-[200px] sm:min-w-[250px] max-w-[90vw] sm:max-w-[400px] animate-in fade-in-0 zoom-in-95 duration-200 ${
                                        dropdownPositionAbove
                                          ? 'bottom-full mb-2'
                                          : 'top-full mt-2'
                                      }`}
                                      onMouseLeave={closeQuickRepliesDropdown}
                                      role="menu"
                                      aria-label="Quick reply options"
                                    >
                                      {/* Scrollable container - max 4 items visible */}
                                      <div
                                        className="max-h-[280px] overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600 scrollbar-track-transparent py-2"
                                        style={{
                                          scrollbarWidth: 'thin',
                                          scrollbarColor: 'rgb(209 213 219) transparent'
                                        }}
                                      >
                                        {message.quickReplies.map((reply: QuickReply, replyIdx: number) => (
                                          <button
                                            key={replyIdx}
                                            onClick={() => {
                                              handleQuickReply(reply);
                                              closeQuickRepliesDropdown();
                                            }}
                                            onMouseEnter={() => setSelectedQuickReplyIndex(replyIdx)}
                                            className={`w-full text-left px-3 sm:px-4 py-2 sm:py-3 text-xs sm:text-sm transition-colors border-b border-gray-100 dark:border-gray-600 last:border-b-0 ${
                                              selectedQuickReplyIndex === replyIdx
                                                ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300'
                                                : 'text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700'
                                            }`}
                                            role="menuitem"
                                            aria-selected={selectedQuickReplyIndex === replyIdx}
                                            style={{
                                              fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
                                            }}
                                          >
                                            <div className="font-medium">{reply.label}</div>
                                            {reply.content !== reply.label && (
                                              <div className={`text-xs mt-1 line-clamp-2 ${
                                                selectedQuickReplyIndex === replyIdx
                                                  ? 'text-blue-600 dark:text-blue-400'
                                                  : 'text-gray-500 dark:text-gray-400'
                                              }`}>
                                                {reply.content.substring(0, 100)}
                                                {reply.content.length > 100 && '...'}
                                              </div>
                                            )}
                                          </button>
                                        ))}
                                      </div>

                                      {/* Scroll indicator when there are more than 4 items */}
                                      {message.quickReplies.length > 4 && (
                                        <div className="absolute bottom-0 left-0 right-0 h-4 bg-gradient-to-t from-white dark:from-gray-800 to-transparent pointer-events-none rounded-b-lg flex items-end justify-center pb-1">
                                          <div className="text-xs text-gray-400 dark:text-gray-500">
                                            ⋯
                                          </div>
                                        </div>
                                      )}
                                    </div>
                                  )}
                                </div>
                              )}
                          </div>
                        )}
                    </div>
                  </div>
                )}

                {/* Remove message-specific quick replies - now shown globally above input */}
              </div>
            ))}

            {/* Loading indicator */}
            {isLoading && (
              <div className="flex justify-start w-full">
                <div className="w-full mx-1">
                  <div
                    className="text-gray-900 dark:text-gray-100 font-sans"
                    style={{
                      fontFamily:
                        '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
                    }}
                  >
                    <div className="flex items-center gap-2">
                      <div className="flex gap-1">
                        <div
                          className="w-1.5 h-1.5 bg-gray-400 dark:bg-gray-500 rounded-full animate-bounce"
                          style={{ animationDelay: "0ms" }}
                        ></div>
                        <div
                          className="w-1.5 h-1.5 bg-gray-400 dark:bg-gray-500 rounded-full animate-bounce"
                          style={{ animationDelay: "200ms" }}
                        ></div>
                        <div
                          className="w-1.5 h-1.5 bg-gray-400 dark:bg-gray-500 rounded-full animate-bounce"
                          style={{ animationDelay: "400ms" }}
                        ></div>
                      </div>
                      <span className="text-sm text-gray-500 dark:text-gray-400">
                        Thinking
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            )}

            <div ref={messagesEndRef} />
          </div>

          {/* Input Area Container with Prompt Manager Integration */}
          <div className={`bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700 mt-auto flex flex-col transition-all duration-300 ${
            promptManager.isOpen ? 'backdrop-blur-sm' : ''
          }`}>


            {/* Input Area - Now positioned first */}
            <div className={`p-2 sm:p-3 md:p-4 relative transition-all duration-500 ease-out ${
              promptManager.isOpen
                ? 'transform translate-y-0 shadow-lg'
                : 'transform translate-y-0'
            }`}
              style={{
                transitionTimingFunction: promptManager.isOpen
                  ? 'cubic-bezier(0.16, 1, 0.3, 1)'
                  : 'cubic-bezier(0.7, 0, 0.84, 0)'
              }}
            >

              {/* Slash Commands Dropdown */}
              {showSlashCommands && filteredSlashPrompts.length > 0 && (
                <div
                  ref={slashDropdownRef}
                  className="slash-commands-dropdown absolute bottom-full left-2 right-2 mb-1 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-50 max-h-48 overflow-y-auto animate-in slide-in-from-bottom-2 fade-in-0 duration-200"
                  role="listbox"
                  aria-label="Prompt suggestions"
                >
                  {/* Header with keyboard hint */}
                  <div className="px-3 py-2 text-xs text-gray-500 dark:text-gray-400 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800/50 rounded-t-lg">
                    <div className="flex items-center justify-between">
                      <span>Choose a prompt</span>
                      <span className="flex items-center gap-1">
                        <kbd className="px-1 py-0.5 text-xs bg-gray-200 dark:bg-gray-700 rounded">↑↓</kbd>
                        <kbd className="px-1 py-0.5 text-xs bg-gray-200 dark:bg-gray-700 rounded">Enter</kbd>
                        <kbd className="px-1 py-0.5 text-xs bg-gray-200 dark:bg-gray-700 rounded">Esc</kbd>
                      </span>
                    </div>
                  </div>
                  {filteredSlashPrompts.map((prompt, index) => (
                    <button
                      key={prompt.id}
                      onClick={() => {
                        dispatch(actions.sendUserPrompt({
                          displayText: prompt.title,
                          content: prompt.content,
                        }));
                        setInputMessage('');
                        setShowSlashCommands(false);
                        setSlashQuery('');
                        setSelectedSlashIndex(0);
                      }}
                      onMouseEnter={() => setSelectedSlashIndex(index)}
                      className={`w-full text-left px-3 py-2 transition-all duration-150 border-l-2 focus:outline-none ${
                        index === selectedSlashIndex
                          ? 'bg-blue-50 dark:bg-blue-900/20 border-blue-500 shadow-sm'
                          : 'border-transparent hover:bg-gray-100 dark:hover:bg-gray-700'
                      } ${index < filteredSlashPrompts.length - 1 ? 'border-b border-gray-200 dark:border-gray-700' : ''} ${
                        index === 0 ? 'rounded-t-lg' : ''
                      } ${index === filteredSlashPrompts.length - 1 ? 'rounded-b-lg' : ''}`}
                      role="option"
                      aria-selected={index === selectedSlashIndex}
                    >
                      <div className={`font-medium text-sm truncate transition-colors ${
                        index === selectedSlashIndex
                          ? 'text-blue-900 dark:text-blue-100'
                          : 'text-gray-900 dark:text-gray-100'
                      }`}>
                        {prompt.title}
                      </div>
                      <div className={`text-xs truncate mt-0.5 transition-colors ${
                        index === selectedSlashIndex
                          ? 'text-blue-700 dark:text-blue-300'
                          : 'text-gray-600 dark:text-gray-400'
                      }`}>
                        {prompt.content.length > 60 ? `${prompt.content.substring(0, 60)}...` : prompt.content}
                      </div>
                    </button>
                  ))}
                </div>
              )}

              <div className="relative">
                <textarea
                  ref={textareaRef}
                  placeholder={
                    isAuth
                      ? isLoading
                        ? "AI is thinking..."
                        : "Ask AI anything..."
                      : "Sign in to use AI"
                  }
                  value={inputMessage}
                  onChange={handleInputChange}
                  onKeyDown={handleKeyPress}
                  disabled={!isAuth || isLoading}
                  className={`w-full pl-3 sm:pl-4 pt-3 sm:pt-4 pb-12 sm:pb-14 md:pb-16 pr-10 sm:pr-14 md:pr-18 border border-gray-300 dark:border-gray-600 rounded-xl sm:rounded-2xl resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent disabled:bg-gray-50 dark:disabled:bg-gray-800 disabled:text-gray-500 dark:disabled:text-gray-400 text-sm sm:text-base leading-relaxed font-normal transition-all duration-200 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400`}
                  style={{ minHeight: "70px" }}
                  autoFocus={isAuth}
                  rows={1}
                />

                {/* Button container for responsive layout */}
                <div className="absolute bottom-2 sm:bottom-3 md:bottom-4 left-2 sm:left-3 md:left-4 right-2 sm:right-3 md:right-4 flex items-center justify-between gap-1 sm:gap-2 min-w-0">
                  {/* Prompts button */}
                  <button
                    onClick={() => dispatch(actions.openPromptManager())}
                    disabled={!isAuth}
                    className={`px-2 sm:px-3 md:px-4 py-1 sm:py-1.5 md:py-2 rounded-lg sm:rounded-xl text-xs sm:text-sm font-medium transition-all duration-300 disabled:cursor-not-allowed transform hover:scale-105 active:scale-95 flex-shrink-0 min-w-0 max-w-[100px] sm:max-w-[120px] md:max-w-none ${
                      promptManager.isOpen
                        ? 'bg-blue-100 hover:bg-blue-200 dark:bg-blue-900 dark:hover:bg-blue-800 text-blue-700 dark:text-blue-300 shadow-md'
                        : 'bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-600 dark:text-gray-300'
                    } disabled:bg-gray-50 dark:disabled:bg-gray-800 disabled:text-gray-400 dark:disabled:text-gray-500`}
                    title={promptManager.isOpen ? "Close prompts" : "Open prompts"}
                    aria-label={promptManager.isOpen ? "Close prompt manager" : "Open prompt manager"}
                  >
                    <span className="hidden sm:inline truncate">
                      {promptManager.isOpen ? '✓ Prompts' : 'Prompts'}
                    </span>
                    <span className="sm:hidden">
                      {promptManager.isOpen ? '✓' : 'P'}
                    </span>
                  </button>

                  {/* Spacer to push send button to the right */}
                  <div className="flex-1 min-w-1 sm:min-w-2"></div>

                  {/* Send button */}
                  <button
                    onClick={handleSendMessage}
                    disabled={!isAuth || isLoading || !inputMessage.trim()}
                    className="w-7 h-7 sm:w-8 sm:h-8 md:w-10 md:h-10 bg-gray-400 hover:bg-gray-500 dark:bg-gray-600 dark:hover:bg-gray-500 disabled:bg-gray-300 dark:disabled:bg-gray-700 text-white rounded-full flex items-center justify-center transition-all duration-200 disabled:cursor-not-allowed flex-shrink-0"
                    title={isAuth ? (isLoading ? "Sending..." : "Send message") : "Sign in to send"}
                    aria-label={isAuth ? (isLoading ? "Sending message" : "Send message") : "Sign in to send message"}
                  >
                    {isLoading ? (
                      <div className="w-3 h-3 sm:w-4 sm:h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    ) : (
                      <Send size={12} className="sm:w-3.5 sm:h-3.5 md:w-4 md:h-4" />
                    )}
                  </button>
                </div>
              </div>
            </div>

            {/* Prompt Manager - Positioned after input area */}
            <div
              className={`overflow-hidden transition-all duration-500 ease-out ${
                promptManager.isOpen
                  ? 'max-h-[60vh] sm:max-h-[65vh] md:max-h-[70vh] opacity-100 transform translate-y-0'
                  : 'max-h-0 opacity-0 transform translate-y-2'
              }`}
              style={{
                transitionProperty: 'max-height, opacity, transform',
                transitionTimingFunction: promptManager.isOpen
                  ? 'cubic-bezier(0.16, 1, 0.3, 1)'
                  : 'cubic-bezier(0.7, 0, 0.84, 0)',
                minHeight: promptManager.isOpen ? '300px' : '0px'
              }}
            >
              {promptManager.isOpen && (
                <div className="border-t border-gray-200 dark:border-gray-700 h-full">
                  <PromptManager onPopulateInput={handlePromptPopulation} />
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Save Prompt Modal */}
      <SavePromptModal
        isOpen={savePromptModal.isOpen}
        onClose={() => dispatch(actions.closeSavePromptModal())}
        initialContent={savePromptModal.sourceMessage || ''}
      />
    </div>
  );
}
