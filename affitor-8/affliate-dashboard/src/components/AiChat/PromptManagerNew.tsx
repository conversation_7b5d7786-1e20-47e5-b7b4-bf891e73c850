"use client";

import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { X, Search, Plus } from "lucide-react";
import {
  actions,
  selectPromptManager,
  selectUserPrompts,
  selectUserPromptsLoading,
  selectUserPromptsError,

  selectRecentPrompts,
  UserPrompt,
} from "@/features/aiscript/aiscript.slice";
import PromptList from "./PromptList";
import PromptEditor from "./PromptEditor";
import PromptDetailView from "./PromptDetailView";

interface PromptManagerProps {
  onPopulateInput?: (content: string) => void;
}

export default function PromptManager({ onPopulateInput }: PromptManagerProps = {}) {
  const dispatch = useDispatch();
  const promptManager = useSelector(selectPromptManager);
  const userPrompts = useSelector(selectUserPrompts);
  const isLoading = useSelector(selectUserPromptsLoading);
  const error = useSelector(selectUserPromptsError);

  const recentPrompts = useSelector(selectRecentPrompts);

  const [showEditor, setShowEditor] = useState(false);
  const [editingPrompt, setEditingPrompt] = useState<UserPrompt | null>(null);
  const [showDetailView, setShowDetailView] = useState(false);
  const [selectedPrompt, setSelectedPrompt] = useState<UserPrompt | null>(null);

  // Fetch prompts when component mounts or when modal opens
  useEffect(() => {
    if (promptManager.isOpen && userPrompts.length === 0) {
      dispatch(actions.fetchUserPrompts());
    }
  }, [promptManager.isOpen, userPrompts.length, dispatch]);

  // Filter prompts based on current view and search
  const filteredPrompts = React.useMemo(() => {
    let prompts = userPrompts;

    // Apply view filter
    if (promptManager.view === "favorites") {
      prompts = prompts.filter((p) => p.isFavorite);
    } else if (promptManager.view === "recent") {
      prompts = recentPrompts;
    } else if (promptManager.view === "templates") {
      prompts = []; // Templates will show coming soon message
    }

    // Apply search filter
    if (promptManager.searchQuery) {
      const query = promptManager.searchQuery.toLowerCase();
      prompts = prompts.filter(
        (p) =>
          p.title.toLowerCase().includes(query) ||
          p.content.toLowerCase().includes(query) ||
          p.description?.toLowerCase().includes(query) ||
          p.tags?.some((tag) => tag.toLowerCase().includes(query))
      );
    }

    // Apply tag filter
    if (promptManager.selectedTags.length > 0) {
      prompts = prompts.filter((p) =>
        p.tags?.some((tag) => promptManager.selectedTags.includes(tag))
      );
    }

    return prompts;
  }, [
    userPrompts,
    recentPrompts,
    promptManager.view,
    promptManager.searchQuery,
    promptManager.selectedTags,
  ]);

  const handleClose = () => {
    dispatch(actions.closePromptManager());
  };

  const handleCreateNew = () => {
    setEditingPrompt(null);
    setShowEditor(true);
  };

  const handleEditPrompt = (prompt: UserPrompt) => {
    setEditingPrompt(prompt);
    setShowEditor(true);
  };

  const handleDeletePrompt = (promptId: string) => {
    dispatch(actions.deleteUserPrompt(promptId));
  };

  const handleToggleFavorite = (promptId: string) => {
    dispatch(actions.toggleUserPromptFavorite(promptId));
  };

  const handleUsePrompt = (prompt: UserPrompt) => {
    dispatch(
      actions.sendUserPrompt({
        displayText: prompt.title,
        content: prompt.content,
      })
    );
    handleClose();
  };

  const handleViewDetail = (prompt: UserPrompt) => {
    setSelectedPrompt(prompt);
    setShowDetailView(true);
  };

  const handleBackToList = () => {
    setShowDetailView(false);
    setSelectedPrompt(null);
  };

  const handleViewChange = (
    view: "all" | "favorites" | "recent" | "templates"
  ) => {
    dispatch(actions.setPromptManagerView(view));
  };

  const handleSearchChange = (query: string) => {
    dispatch(actions.setPromptManagerSearch(query));
  };

  return (
      <div className="bg-white dark:bg-gray-900 flex flex-col h-full min-h-0 transform transition-all duration-500 ease-out animate-in slide-in-from-bottom-4 fade-in-0">
      <div className="flex items-center justify-between p-2 sm:p-3 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 flex-shrink-0">
        <h2 className="text-base sm:text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center gap-2">
          <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
          Prompts
        </h2>
        <button
          onClick={handleClose}
          className="p-1 sm:p-1.5 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md transition-all duration-200 hover:scale-105 active:scale-95"
          title="Close prompt manager"
          aria-label="Close prompt manager"
        >
          <X size={16} className="sm:w-[18px] sm:h-[18px] text-gray-500 dark:text-gray-400" />
        </button>
      </div>

      <div className="p-2 sm:p-3 border-b border-gray-200 dark:border-gray-700 flex-shrink-0">
        <div className="relative mb-2">
          <Search
            size={14}
            className="sm:w-4 sm:h-4 absolute left-2 sm:left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
          />
          <input
            type="text"
            placeholder="Search prompts..."
            value={promptManager.searchQuery}
            onChange={(e) => handleSearchChange(e.target.value)}
            className="w-full pl-8 sm:pl-10 pr-3 sm:pr-4 py-2 sm:py-2.5 border border-blue-300 dark:border-blue-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm sm:text-base"
          />
        </div>

        <div className="flex items-center gap-1 overflow-x-auto scrollbar-none">
          <button
            onClick={() => handleViewChange("all")}
            className={`flex items-center gap-1 sm:gap-2 px-2 sm:px-4 py-2 text-xs sm:text-sm font-medium transition-colors whitespace-nowrap ${
              promptManager.view === "all"
                ? "text-blue-600 dark:text-blue-400 border-b-2 border-blue-600 dark:border-blue-400"
                : "text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200"
            }`}
          >
            Your Saved ({filteredPrompts.length})
          </button>
          <button
            onClick={() => handleViewChange("templates")}
            className={`flex items-center gap-1 sm:gap-2 px-2 sm:px-4 py-2 text-xs sm:text-sm font-medium transition-colors whitespace-nowrap ${
              promptManager.view === "templates"
                ? "text-blue-600 dark:text-blue-400 border-b-2 border-blue-600 dark:border-blue-400"
                : "text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200"
            }`}
          >
            Templates
          </button>
        </div>
      </div>

      <div className="flex-1 overflow-hidden flex flex-col relative min-h-0">
        {/* Conditional rendering based on current view */}
        {showDetailView && selectedPrompt ? (
          <PromptDetailView
            prompt={selectedPrompt}
            onClose={handleClose}
            onBack={handleBackToList}
            onUse={handleUsePrompt}
            onPopulateInput={onPopulateInput}
          />
        ) : (
          <>
            <div className="flex-1 overflow-y-auto min-h-0" style={{ maxHeight: 'calc(100% - 70px)' }}>
              <PromptList
                prompts={filteredPrompts}
                isLoading={isLoading}
                error={error}
                onEdit={handleEditPrompt}
                onDelete={handleDeletePrompt}
                onToggleFavorite={handleToggleFavorite}
                onUse={handleUsePrompt}
                onViewDetail={handleViewDetail}
                view={promptManager.view}
              />
            </div>

            {/* Create new prompt button - Fixed at bottom */}
            <div className="flex-shrink-0 p-2 sm:p-3 border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900 sticky bottom-0">
              <button
                onClick={handleCreateNew}
                className="w-full flex items-center justify-center gap-1 sm:gap-2 px-3 sm:px-4 py-2 sm:py-3 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg text-gray-600 dark:text-gray-400 hover:border-gray-400 dark:hover:border-gray-500 hover:text-gray-700 dark:hover:text-gray-300 transition-colors text-sm sm:text-base"
                title="Create a new prompt"
                aria-label="Create a new prompt"
              >
                <Plus size={14} className="sm:w-4 sm:h-4" />
                Create new prompt
              </button>
            </div>
          </>
        )}
      </div>
      {/* PromptEditor - Overlay when active */}
      {showEditor && (
        <div className="absolute inset-0 z-10 bg-white dark:bg-gray-900 mt-[1px]">
          <PromptEditor
            prompt={editingPrompt}
            onSave={() => {
              setShowEditor(false);
              setEditingPrompt(null);
              dispatch(actions.fetchUserPrompts());
            }}
            onCancel={() => {
              setShowEditor(false);
              setEditingPrompt(null);
            }}
          />
        </div>
      )}
    </div>
  );
}
